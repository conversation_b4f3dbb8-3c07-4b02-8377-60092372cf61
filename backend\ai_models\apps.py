from django.apps import AppConfig


class AiModelsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'ai_models'
    verbose_name = 'AI Models - Predictive Analytics & Machine Learning'
    
    def ready(self):
        """Initialize AI models when Django starts"""
        # DISABLED: AI model loading moved to lazy initialization
        # This was causing Django startup to hang
        print("AI Models app ready - using lazy loading for performance")
