"""
Working URL configuration - only includes enabled apps
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

# Working URL patterns - only for enabled apps
urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),
    
    # Language switch URL
    path('i18n/', include('django.conf.urls.i18n')),
    
    # API endpoints (only for enabled apps)
    path('api/auth/', include('rest_framework.urls')),
    path('api/users/', include('users.urls')),
    path('api/forums/', include('forums.urls')),
    path('api/', include('api.urls')),
    path('api/superadmin/', include('superadmin.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
