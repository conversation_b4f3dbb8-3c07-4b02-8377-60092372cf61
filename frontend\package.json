{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:analyze": "vite build --mode analyze", "build:performance": "vite build --config vite.config.performance.ts", "lint": "eslint .", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:hooks": "jest --testPathPattern=hooks/__tests__", "test:components": "jest --testPathPattern=components/admin/", "test:e2e": "playwright test", "test:crud": "jest --testPathPattern='(hooks/__tests__|components/admin/.*/__tests__|__tests__/e2e/)'", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:update-snapshots": "jest --updateSnapshot", "theme:audit": "node scripts/theme-audit-enhanced.js", "i18n:audit": "node scripts/translation-audit.js", "i18n:manage": "node scripts/translation-manager.js", "translate:auto": "node scripts/translation-automation.js", "translate:priority": "node scripts/translation-priority.js", "translate:fix": "node scripts/runtime-translation-fixer.js", "clean": "rimraf dist node_modules/.cache", "clean:all": "rimraf dist node_modules/.cache node_modules"}, "dependencies": {"@reduxjs/toolkit": "^2.8.1", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-devtools": "^5.76.1", "axios": "^1.9.0", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "i18next": "^25.1.3", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-i18next": "^15.5.1", "react-quill": "^2.0.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0", "recharts": "^2.15.3", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@playwright/test": "^1.52.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "babel-jest": "^29.7.0", "chalk": "^4.1.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-html-reporters": "^3.1.7", "jest-junit": "^16.0.0", "jest-sonar-reporter": "^2.0.0", "jest-watch-typeahead": "^2.2.2", "msw": "^2.9.0", "postcss": "^8.4.35", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^5.12.0", "tailwindcss": "^3.4.1", "terser": "^5.39.2", "ts-jest": "^29.3.4", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vite-plugin-compression": "^0.5.1"}}